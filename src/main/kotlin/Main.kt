package org.example

import eu.mihosoft.jcsg.Cube
import javafx.application.Application
import javafx.scene.PerspectiveCamera
import javafx.scene.Scene
import javafx.scene.SceneAntialiasing
import javafx.scene.input.KeyCode
import javafx.scene.layout.StackPane
import javafx.scene.paint.Color
import javafx.scene.paint.PhongMaterial
import javafx.scene.shape.MeshView
import javafx.scene.transform.Scale
import javafx.scene.transform.Translate
import javafx.stage.Stage
import kotlinx.coroutines.*
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

class CadViewer: Application() {
    var zoom = 1.0
    var xOffset: Double = 0.0;
    var yOffset: Double = 0.0;
    private val scope = CoroutineScope(Dispatchers.Default)
    lateinit var meshView: MeshView;
    @OptIn(DelicateCoroutinesApi::class)

    override fun start(primaryStage: Stage?) {
        val cube = Cube(100.0, 100.0, 100.0).toCSG()
        val meshContainer = cube.toJavaFXMesh()
        meshView = meshContainer.getAsMeshViews()[0];
        meshView.material = PhongMaterial(Color.ALICEBLUE)
        val root = StackPane()
        root.children.add(meshView)
        primaryStage!!.title = "Visualizador CAD"
        primaryStage.scene = Scene(root, 800.0, 600.0, true, SceneAntialiasing.BALANCED)
        primaryStage.scene.camera = PerspectiveCamera(false)
        primaryStage.scene.fill = Color.GRAY
        primaryStage.show()
        setupControls(primaryStage)
    }

    @OptIn(DelicateCoroutinesApi::class, ExperimentalTime::class)
    private fun setupControls(primaryStage: Stage) {
        val keysPressed = object {
            var w = false;
            var s = false;
            var a = false;
            var d = false;
            var space = false;
            var shift = false;
        }
        fun setValue(code: KeyCode?, value: Boolean) {
            when (code) {
                KeyCode.W -> keysPressed.w = value;
                KeyCode.S -> keysPressed.s = value;
                KeyCode.A -> keysPressed.a = value;
                KeyCode.D -> keysPressed.d = value;
                KeyCode.SHIFT -> keysPressed.shift = value;
                KeyCode.SPACE -> keysPressed.space = value;
                else -> {}
            }
        }
        primaryStage.scene.setOnKeyPressed { setValue(it.code, true) }
        primaryStage.scene.setOnKeyReleased { setValue(it.code, false) }
        GlobalScope.launch(Dispatchers.Main) {
            while (true) {
                val lastExec = Clock.System.now().epochSeconds
                if (keysPressed.w) {
                    zoomIn()
                } else if (keysPressed.s) {
                    zoomOut()
                }
                if (keysPressed.a) {
                    left()
                } else if (keysPressed.d) {
                    right()
                }
                if (keysPressed.space) {
                    up()
                } else if (keysPressed.shift) {
                    down()
                }
                delay(100 / 6 - (Clock.System.now().epochSeconds - lastExec))
            }
        }
    }

    fun updateTransforms(r: Runnable) {
        r.run()
        meshView.transforms.clear()
        meshView.transforms.addAll(
            Scale(zoom, zoom, zoom),
            Translate(xOffset, yOffset, 0.0),

        )
    }

    fun zoomIn() = updateTransforms {zoom *= 1.05}
    fun zoomOut() = updateTransforms {zoom *= 1.0/1.05}
    fun left() = updateTransforms {xOffset-=2}
    fun right() = updateTransforms {xOffset+=2}
    fun down() = updateTransforms {yOffset-=2}
    fun up() = updateTransforms {yOffset+=2}
}
fun main() {
    Application.launch(CadViewer::class.java);
}